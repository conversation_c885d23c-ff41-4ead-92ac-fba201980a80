const { uploadImage, startAnalysis, getAnalysisResult } = require('../services/youcamService');

// Test endpoint to debug API issues with limited polling
exports.testAnalysis = async (req, res) => {
  try {
    console.log('=== TESTING ANALYSIS FLOW ===');

    // Create a simple test image (1x1 pixel red PNG in base64)
    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

    console.log('Step 1: Testing image upload...');
    const fileId = await uploadImage(testImageBase64);
    console.log('✓ Upload successful, fileId:', fileId);

    console.log('Step 2: Testing analysis start...');
    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    console.log('Step 3: Testing limited polling (5 attempts only)...');
    try {
      // Test just a few polling attempts to see the response format
      const result = await testLimitedPolling(taskId, 5);
      console.log('✓ Limited polling completed');

      res.json({
        success: true,
        message: 'Test completed with limited polling',
        fileId,
        taskId,
        pollingResult: result,
        note: 'Check server logs for detailed analysis flow.'
      });
    } catch (pollingError) {
      console.log('Polling test completed with expected timeout');
      res.json({
        success: true,
        message: 'Upload and analysis start working, polling shows expected behavior',
        fileId,
        taskId,
        pollingError: pollingError.message,
        note: 'Tiny test image likely cannot be analyzed for facial features - this is expected.'
      });
    }

  } catch (error) {
    console.error('Test failed:', error);
    res.status(500).json({ error: error.message, step: 'Test analysis flow' });
  }
};

// Helper function for limited polling test
async function testLimitedPolling(taskId, maxAttempts = 5) {
  const https = require('https');
  const { getAccessToken } = require('../services/youcamService');

  console.log('Testing limited polling for task ID:', taskId);
  const accessToken = await getAccessToken();

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    const options = {
      method: 'GET',
      hostname: 'yce-api-01.perfectcorp.com',
      port: null,
      path: `/s2s/v1.0/task/face-attr-analysis?task_id=${encodeURIComponent(taskId)}`,
      headers: { Authorization: `Bearer ${accessToken}` }
    };

    const result = await new Promise((resolve, reject) => {
      const req = https.request(options, function (res) {
        const chunks = [];
        res.on('data', chunk => chunks.push(chunk));
        res.on('end', function () {
          const body = Buffer.concat(chunks);
          const responseText = body.toString();
          try {
            const responseData = JSON.parse(responseText);
            if (res.statusCode >= 200 && res.statusCode < 300) {
              resolve(responseData);
            } else {
              reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
            }
          } catch (parseError) {
            reject(new Error(`HTTP ${res.statusCode}: ${responseText}`));
          }
        });
      });
      req.on('error', error => reject(new Error(`Request failed: ${error.message}`)));
      req.end();
    });

    console.log(`Polling attempt ${attempt + 1}:`, JSON.stringify(result, null, 2));

    const taskStatus = result.result?.status;
    if (taskStatus === 'success') {
      return result.result.results;
    }
    if (taskStatus === 'error' || taskStatus === 'failed') {
      throw new Error(`Analysis failed: ${result.result?.error_message || 'Unknown error'}`);
    }

    await new Promise(r => setTimeout(r, 1000)); // Wait 1 second between attempts
  }

  throw new Error(`Limited polling completed - status still running after ${maxAttempts} attempts`);
}

exports.analyzeFace = async (req, res) => {
  try {
    console.log('Received request body keys:', Object.keys(req.body));
    
    // Handle both data URL format and direct base64
    let img = req.body.image;
    if (!img) return res.status(400).json({ error: 'No image provided' });
    
    // If it's a data URL, extract the base64 part
    if (img.includes(',')) {
      const parts = img.split(',');
      if (parts.length !== 2) {
        return res.status(400).json({ error: 'Invalid data URL format' });
      }
      img = parts[1];
    }
    
    // Validate base64 format
    if (!img || img.length < 100) {
      return res.status(400).json({ error: 'Invalid or too short base64 image data' });
    }
    
    // Basic base64 validation
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Pattern.test(img)) {
      return res.status(400).json({ error: 'Invalid base64 format' });
    }
    
    console.log('Processing image of length:', img.length);
    console.log('Image starts with:', img.substring(0, 50));

    const fileId = await uploadImage(img);
    console.log('File uploaded with ID:', fileId);
    
    const taskId = await startAnalysis(fileId);
    console.log('Analysis started with task ID:', taskId);
    
    const result = await getAnalysisResult(taskId);
    console.log('Analysis completed');

    res.json({ success: true, fileId, taskId, result });
  } catch (error) {
    console.error('Error in analyzeFace:', error);
    res.status(500).json({ error: error.message });
  }
};
