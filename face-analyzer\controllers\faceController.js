const { uploadImage, startAnalysis, getAnalysisResult } = require('../services/youcamService');

// Test endpoint to debug API issues
exports.testAnalysis = async (req, res) => {
  try {
    console.log('=== TESTING ANALYSIS FLOW ===');

    // Create a simple test image (1x1 pixel red PNG in base64)
    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';

    console.log('Step 1: Testing image upload...');
    const fileId = await uploadImage(testImageBase64);
    console.log('✓ Upload successful, fileId:', fileId);

    console.log('Step 2: Testing analysis start...');
    const taskId = await startAnalysis(fileId);
    console.log('✓ Analysis started, taskId:', taskId);

    console.log('Step 3: Analysis flow test completed');

    res.json({
      success: true,
      message: 'Test completed successfully - upload and analysis start working',
      fileId,
      taskId,
      note: 'Check server logs for detailed analysis flow. Full polling test not run to avoid timeout.'
    });

  } catch (error) {
    console.error('Test failed:', error);
    res.status(500).json({ error: error.message, step: 'Test analysis flow' });
  }
};

exports.analyzeFace = async (req, res) => {
  try {
    console.log('Received request body keys:', Object.keys(req.body));
    
    // Handle both data URL format and direct base64
    let img = req.body.image;
    if (!img) return res.status(400).json({ error: 'No image provided' });
    
    // If it's a data URL, extract the base64 part
    if (img.includes(',')) {
      const parts = img.split(',');
      if (parts.length !== 2) {
        return res.status(400).json({ error: 'Invalid data URL format' });
      }
      img = parts[1];
    }
    
    // Validate base64 format
    if (!img || img.length < 100) {
      return res.status(400).json({ error: 'Invalid or too short base64 image data' });
    }
    
    // Basic base64 validation
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Pattern.test(img)) {
      return res.status(400).json({ error: 'Invalid base64 format' });
    }
    
    console.log('Processing image of length:', img.length);
    console.log('Image starts with:', img.substring(0, 50));

    const fileId = await uploadImage(img);
    console.log('File uploaded with ID:', fileId);
    
    const taskId = await startAnalysis(fileId);
    console.log('Analysis started with task ID:', taskId);
    
    const result = await getAnalysisResult(taskId);
    console.log('Analysis completed');

    res.json({ success: true, fileId, taskId, result });
  } catch (error) {
    console.error('Error in analyzeFace:', error);
    res.status(500).json({ error: error.message });
  }
};
